import asyncio
from bleak import BleakClient, BleakScanner
import struct
import contextlib

DEVICE_ADDRESS = "CF:C3:A0:A2:BE:15"

# Notify-capable characteristics
UUIDS = {
    "FEV1": "f9f84150-b667-11e3-a5e2-0800200c9a66",
    "FVC": "f9f84151-b667-11e3-a5e2-0800200c9a66",
    "FEV1/FVC": "f9f84152-b667-11e3-a5e2-0800200c9a66",
    "PEF": "f9f84153-b667-11e3-a5e2-0800200c9a66",
    "STATUS": "7d32c0f0-bef5-11e3-b1b6-0800200c9a66"
}

# Command characteristic
COMMAND_CHAR = "92b403f0-b665-11e3-a5e2-0800200c9a66"

# Commands as used by the official app
ENABLE_CMD = bytes.fromhex("01 00 00 00 00 00")  # Enable measurement
GET_RESULT_CMD = bytes.fromhex("02 00 00 00 00 00")  # Get last result


def decode(name, data):
    if not data:
        print(f"{name}: No data received.")
        return

    print(f"\n{name} | Raw: {data.hex()} | Bytes: {list(data)}")

    try:
        if name == "FEV1":
            if len(data) >= 2:
                raw_val = struct.unpack('<H', data[:2])[0]
                val = raw_val / 100.0
                print(f"FEV1 raw: {data.hex()}  ->  FEV1: {val:.2f} L")
            else:
                print(f"⚠️ Unexpected FEV1 data length: {len(data)}")
        elif name == "FVC":
            if len(data) >= 2:
                raw_val = struct.unpack('<H', data[:2])[0]
                val = raw_val / 100.0
                print(f"FVC raw: {data.hex()}  ->  FVC: {val:.2f} L")
            else:
                print(f"⚠️ Unexpected FVC data length: {len(data)}")
        elif name == "FEV1/FVC":
            if len(data) >= 1:
                val = data[0] if len(data) == 1 else struct.unpack(
                    '<H', data[:2])[0]
                print(f"RATIO raw: {data.hex()}  ->  FEV1/FVC: {val}%")
            else:
                print(f"⚠️ Unexpected FEV1/FVC data length: {len(data)}")
        elif name == "PEF":
            if len(data) >= 2:
                raw_val = struct.unpack('<H', data[:2])[0]
                val = raw_val / 10.0
                print(f"PEF raw: {data.hex()}  ->  PEF : {val:.1f} L/s")
            else:
                print(f"⚠️ Unexpected PEF data length: {len(data)}")
        elif name == "STATUS":
            if len(data) >= 1:
                status = data[0]
                status_map = {0: "Idle", 1: "Measuring", 2: "Test Complete"}
                status_text = status_map.get(status, f"Unknown ({status})")
                print(f"STATUS raw: {data.hex()}  ->  {status_text}")
            else:
                print(f"⚠️ Unexpected STATUS data length: {len(data)}")
    except Exception as e:
        print(f"⚠️ Error decoding {name}: {e}")


async def main():
    print(f"🔍 Scanning for MIR Smart One device: {DEVICE_ADDRESS}")
    device = await BleakScanner.find_device_by_address(DEVICE_ADDRESS)

    if not device:
        print(f"❌ Could not find device {DEVICE_ADDRESS}")
        return

    print(f"📱 Found device: {device.name or 'MIR Smart One'}")

    async with BleakClient(device) as client:
        print("✅ Connected to MIR Smart One")

        # Attempt pairing once per machine
        try:
            print("🔐 Attempting to pair with device...")
            paired = await client.pair(protection_level="medium")
            if paired:
                print("✅ Successfully paired with encryption!")
            else:
                print("❌ Pairing failed or device already paired")
        except Exception as e:
            print(f"❌ Pairing failed or device already paired: {e}")

        # Check device status and unlock if needed
        print("\n🔓 Checking device status...")
        try:
            status_data = await client.read_gatt_char(UUIDS["STATUS"])
            if status_data and len(status_data) > 0:
                print(f"Initial status: {status_data.hex()}")
                if status_data[0] == 0x1b:
                    print("🔓 Device in protected mode, sending unlock command...")
                    unlock_cmd = bytes.fromhex("1b 00 00 00 00 00")
                    await client.write_gatt_char(COMMAND_CHAR, unlock_cmd, response=False)
                    await asyncio.sleep(1)
                    print("✅ Unlock command sent")
        except Exception as e:
            print(f"❌ Could not check/unlock device status: {e}")

        # Setup notifications
        print("\n📡 Setting up notifications...")

        def make_callback(name):
            def callback(sender, data):
                decode(name, data)
            return callback

        for name, uuid in UUIDS.items():
            try:
                callback = make_callback(name)
                await client.start_notify(uuid, callback)
                print(f"✅ Subscribed to {name}")
            except Exception as e:
                print(f"❌ Could not subscribe to {name}: {e}")

        # Clear previous state if any
        print("\n🧹 Clearing previous state...")
        try:
            clear_cmd = bytes.fromhex("00 00 00 00 00 00")
            await client.write_gatt_char(COMMAND_CHAR, clear_cmd, response=False)
            await asyncio.sleep(1)
            print("✅ State cleared")
        except Exception as e:
            print(f"❌ Could not clear state: {e}")

        # Send enable measurement command (6 bytes)
        print("\n🚀 Sending enable measurement command...")
        try:
            await client.write_gatt_char(COMMAND_CHAR, ENABLE_CMD, response=False)
            print("✅ Measurement command sent!")
            print("📡 Device should now be ready to receive notifications...")
        except Exception as e:
            print(f"❌ Failed to send enable measurement command: {e}")
            return

        print("\n" + "="*60)
        print("📢 SPIROMETRY TEST INSTRUCTIONS")
        print("="*60)
        print("🔍 Watching for notifications...")

        # Listen for notifications with periodic status checks
        for i in range(12):  # 60 seconds total, check every 5 seconds
            await asyncio.sleep(5)
            print(f"⏱️  Still listening... {(i+1)*5}s elapsed")

            # Check status to break early if test is complete
            try:
                status_data = await client.read_gatt_char(UUIDS["STATUS"])
                if status_data and len(status_data) > 0:
                    status_val = status_data[0]
                    if status_val == 2:
                        print("✅ Test completion detected via status read!")
                        break
            except Exception:
                pass  # suppress read errors here

        # Stop notifications on all characteristics
        for uuid in UUIDS.values():
            with contextlib.suppress(Exception):
                await client.stop_notify(uuid)

        print("✅ Listening session complete.")


if __name__ == "__main__":
    asyncio.run(main())
