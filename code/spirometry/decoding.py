import asyncio
from bleak import BleakClient
import struct

DEVICE_ADDRESS = "CF:C3:A0:A2:BE:15"

# Notify-capable characteristics
UUIDS = {
    "FEV1": "f9f84150-b667-11e3-a5e2-0800200c9a66",
    "FVC": "f9f84151-b667-11e3-a5e2-0800200c9a66",
    "FEV1/FVC": "f9f84152-b667-11e3-a5e2-0800200c9a66",
    "PEF": "f9f84153-b667-11e3-a5e2-0800200c9a66",
    "STATUS": "7d32c0f0-bef5-11e3-b1b6-0800200c9a66"
}


def decode(name, data):
    if not data:
        print(f"{name}: No data received.")
        return

    print(f"\n🔔 {name} | Raw: {data.hex()} | Bytes: {list(data)}")

    try:
        if name == "FEV1":
            val = struct.unpack('<H', data[:2])[0] / 100.0
            print(f"📊 FEV1: {val:.2f} L")
        elif name == "FVC":
            val = struct.unpack('<H', data[:2])[0] / 100.0
            print(f"📊 FVC: {val:.2f} L")
        elif name == "FEV1/FVC":
            val = data[0] if len(data) == 1 else struct.unpack(
                '<H', data[:2])[0]
            print(f"📊 FEV1/FVC Ratio: {val}%")
        elif name == "PEF":
            val = struct.unpack('<H', data[:2])[0] / 10.0
            print(f"📊 PEF: {val:.1f} L/s")
        elif name == "STATUS":
            status_map = {0: "Idle", 1: "Measuring", 2: "Test Complete"}
            status = data[0]
            print(f"📶 Status: {status_map.get(status, 'Unknown')}")
    except Exception as e:
        print(f"⚠️ Error decoding {name}: {e}")


async def main():
    async with BleakClient(DEVICE_ADDRESS) as client:
        print("✅ Connected to MIR Smart One\n")

        for name, uuid in UUIDS.items():
            try:
                await client.start_notify(uuid, lambda sender, data, n=name: decode(n, data))
                print(f"📡 Subscribed to {name}")
            except Exception as e:
                print(f"⚠️ Could not subscribe to {name}: {e}")

        print("\n💨 Please blow into the device now... listening for 60s")
        await asyncio.sleep(60)

        for uuid in UUIDS.values():
            try:
                await client.stop_notify(uuid)
            except:
                pass

        print("✅ Listening session complete.")

if __name__ == "__main__":
    asyncio.run(main())
